// 玩家类
class Player {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.width = 24;
        this.height = 32;
        this.vx = 0;
        this.vy = 0;
        this.speed = 5;
        this.jumpPower = 12;
        this.gravity = 0.5;
        this.maxFallSpeed = 15;
        this.onGround = false;
        this.canDoubleJump = false;
        this.hasDoubleJumped = false;
        this.facing = 1; // 1 = 右, -1 = 左
        
        // 动画相关
        this.animFrame = 0;
        this.animTimer = 0;
        this.animSpeed = 8;
        this.state = 'idle'; // idle, running, jumping, falling
        
        // 无敌时间
        this.invulnerable = false;
        this.invulnerableTime = 0;
        this.maxInvulnerableTime = 120; // 2秒
        
        // 视觉效果
        this.squashScale = 1;
        this.landingEffect = false;
    }

    update(keys, platforms, enemies, collectibles, spikes, particleSystem) {
        this.handleInput(keys, particleSystem);
        this.updatePhysics();
        this.checkCollisions(platforms, enemies, collectibles, spikes, particleSystem);
        this.updateAnimation();
        this.updateEffects();
        
        if (this.invulnerable) {
            this.invulnerableTime--;
            if (this.invulnerableTime <= 0) {
                this.invulnerable = false;
            }
        }
    }

    handleInput(keys, particleSystem) {
        // 水平移动
        if (keys['a'] || keys['A'] || keys['ArrowLeft']) {
            this.vx = -this.speed;
            this.facing = -1;
            this.state = this.onGround ? 'running' : this.state;
        } else if (keys['d'] || keys['D'] || keys['ArrowRight']) {
            this.vx = this.speed;
            this.facing = 1;
            this.state = this.onGround ? 'running' : this.state;
        } else {
            this.vx *= 0.8; // 摩擦力
            if (this.onGround && Math.abs(this.vx) < 0.1) {
                this.vx = 0;
                this.state = 'idle';
            }
        }

        // 跳跃
        if ((keys['w'] || keys['W'] || keys[' ']) && !this.jumpPressed) {
            if (this.onGround) {
                this.vy = -this.jumpPower;
                this.onGround = false;
                this.canDoubleJump = true;
                this.hasDoubleJumped = false;
                this.state = 'jumping';
                particleSystem.createJumpDust(this.x + this.width/2, this.y + this.height);
                this.squashScale = 0.8; // 跳跃时压缩效果
            } else if (this.canDoubleJump && !this.hasDoubleJumped) {
                this.vy = -this.jumpPower * 0.8;
                this.hasDoubleJumped = true;
                this.canDoubleJump = false;
                particleSystem.createExplosion(this.x + this.width/2, this.y + this.height/2, '#00ffff', 6);
                this.squashScale = 0.7; // 二段跳更强的压缩
            }
            this.jumpPressed = true;
        }
        
        if (!(keys['w'] || keys['W'] || keys[' '])) {
            this.jumpPressed = false;
        }
    }

    updatePhysics() {
        // 重力
        this.vy += this.gravity;
        if (this.vy > this.maxFallSpeed) {
            this.vy = this.maxFallSpeed;
        }

        // 更新位置
        this.x += this.vx;
        this.y += this.vy;

        // 更新状态
        if (!this.onGround) {
            this.state = this.vy < 0 ? 'jumping' : 'falling';
        }
    }

    checkCollisions(platforms, enemies, collectibles, spikes, particleSystem) {
        this.onGround = false;

        // 平台碰撞
        platforms.forEach(platform => {
            if (this.intersects(platform)) {
                // 从上方落下
                if (this.vy > 0 && this.y < platform.y) {
                    this.y = platform.y - this.height;
                    this.vy = 0;
                    this.onGround = true;
                    this.canDoubleJump = true;
                    this.hasDoubleJumped = false;
                    
                    if (this.landingEffect) {
                        particleSystem.createJumpDust(this.x + this.width/2, this.y + this.height);
                        this.squashScale = 1.2; // 着陆时拉伸效果
                        this.landingEffect = false;
                    }
                }
                // 从下方撞击
                else if (this.vy < 0 && this.y > platform.y) {
                    this.y = platform.y + platform.height;
                    this.vy = 0;
                }
                // 从左侧撞击
                else if (this.vx > 0 && this.x < platform.x) {
                    this.x = platform.x - this.width;
                    this.vx = 0;
                }
                // 从右侧撞击
                else if (this.vx < 0 && this.x > platform.x) {
                    this.x = platform.x + platform.width;
                    this.vx = 0;
                }
            }
        });

        // 收集品碰撞
        for (let i = collectibles.length - 1; i >= 0; i--) {
            if (this.intersects(collectibles[i])) {
                particleSystem.createCoinEffect(collectibles[i].x + collectibles[i].width/2, 
                                              collectibles[i].y + collectibles[i].height/2);
                collectibles.splice(i, 1);
                return { type: 'coin', points: 100 };
            }
        }

        // 尖刺碰撞
        spikes.forEach(spike => {
            if (this.intersects(spike) && !this.invulnerable) {
                this.takeDamage(particleSystem);
            }
        });

        // 敌人碰撞
        enemies.forEach(enemy => {
            if (this.intersects(enemy) && !this.invulnerable) {
                // 如果玩家从上方跳到敌人身上
                if (this.vy > 0 && this.y < enemy.y - 10) {
                    this.vy = -this.jumpPower * 0.6; // 弹跳
                    enemy.takeDamage();
                    particleSystem.createExplosion(enemy.x + enemy.width/2, enemy.y + enemy.height/2, '#ff6600');
                    return { type: 'enemy', points: 200 };
                } else {
                    this.takeDamage(particleSystem);
                }
            }
        });

        // 设置着陆效果标志
        if (!this.onGround && this.vy > 5) {
            this.landingEffect = true;
        }

        return null;
    }

    takeDamage(particleSystem) {
        if (!this.invulnerable) {
            this.invulnerable = true;
            this.invulnerableTime = this.maxInvulnerableTime;
            particleSystem.createDeathEffect(this.x + this.width/2, this.y + this.height/2);
            
            // 击退效果
            this.vy = -8;
            this.vx = this.facing * -3;
            
            return true;
        }
        return false;
    }

    updateAnimation() {
        this.animTimer++;
        if (this.animTimer >= this.animSpeed) {
            this.animTimer = 0;
            this.animFrame = (this.animFrame + 1) % 4;
        }
    }

    updateEffects() {
        // 恢复压缩/拉伸效果
        if (this.squashScale < 1) {
            this.squashScale += 0.05;
            if (this.squashScale > 1) this.squashScale = 1;
        } else if (this.squashScale > 1) {
            this.squashScale -= 0.05;
            if (this.squashScale < 1) this.squashScale = 1;
        }
    }

    intersects(rect) {
        return this.x < rect.x + rect.width &&
               this.x + this.width > rect.x &&
               this.y < rect.y + rect.height &&
               this.y + this.height > rect.y;
    }

    draw(ctx) {
        ctx.save();
        
        // 无敌闪烁效果
        if (this.invulnerable && Math.floor(this.invulnerableTime / 5) % 2) {
            ctx.globalAlpha = 0.5;
        }

        // 应用压缩/拉伸效果
        const centerX = this.x + this.width / 2;
        const centerY = this.y + this.height / 2;
        ctx.translate(centerX, centerY);
        ctx.scale(this.facing, this.squashScale);
        ctx.translate(-this.width / 2, -this.height / 2);

        // 绘制玩家（像素风格）
        this.drawPixelPlayer(ctx);
        
        ctx.restore();
    }

    drawPixelPlayer(ctx) {
        const colors = {
            skin: '#ffdbac',
            shirt: '#ff6b6b',
            pants: '#4ecdc4',
            shoes: '#45b7d1',
            hair: '#8b4513'
        };

        // 头部
        ctx.fillStyle = colors.skin;
        ctx.fillRect(6, 0, 12, 10);
        
        // 头发
        ctx.fillStyle = colors.hair;
        ctx.fillRect(4, 0, 16, 6);
        
        // 眼睛
        ctx.fillStyle = '#000';
        ctx.fillRect(8, 3, 2, 2);
        ctx.fillRect(14, 3, 2, 2);
        
        // 身体
        ctx.fillStyle = colors.shirt;
        ctx.fillRect(4, 10, 16, 12);
        
        // 手臂
        ctx.fillStyle = colors.skin;
        if (this.state === 'running') {
            // 跑步时摆动手臂
            const armOffset = Math.sin(this.animFrame * 0.5) * 2;
            ctx.fillRect(0, 12 + armOffset, 4, 8);
            ctx.fillRect(20, 12 - armOffset, 4, 8);
        } else {
            ctx.fillRect(0, 12, 4, 8);
            ctx.fillRect(20, 12, 4, 8);
        }
        
        // 腿部
        ctx.fillStyle = colors.pants;
        if (this.state === 'running' && this.onGround) {
            // 跑步时腿部动画
            const legOffset = Math.sin(this.animFrame * 0.8) * 3;
            ctx.fillRect(6, 22, 5, 10);
            ctx.fillRect(13, 22, 5, 10);
            
            // 脚部
            ctx.fillStyle = colors.shoes;
            ctx.fillRect(4, 30 + legOffset, 8, 4);
            ctx.fillRect(12, 30 - legOffset, 8, 4);
        } else {
            ctx.fillRect(6, 22, 5, 10);
            ctx.fillRect(13, 22, 5, 10);
            
            // 脚部
            ctx.fillStyle = colors.shoes;
            ctx.fillRect(4, 30, 8, 4);
            ctx.fillRect(12, 30, 8, 4);
        }
    }

    reset(x, y) {
        this.x = x;
        this.y = y;
        this.vx = 0;
        this.vy = 0;
        this.onGround = false;
        this.canDoubleJump = false;
        this.hasDoubleJumped = false;
        this.invulnerable = false;
        this.invulnerableTime = 0;
        this.state = 'idle';
        this.squashScale = 1;
        this.landingEffect = false;
    }
}
