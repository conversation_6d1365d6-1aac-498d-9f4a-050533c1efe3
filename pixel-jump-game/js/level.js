// 关卡系统
class LevelManager {
    constructor() {
        this.currentLevel = 1;
        this.levels = this.createLevels();
    }

    createLevels() {
        return {
            1: {
                name: "新手训练",
                description: "学习基础跳跃技巧",
                playerStart: { x: 50, y: 400 },
                platforms: [
                    { x: 0, y: 550, width: 200, height: 50, type: 'ground' },
                    { x: 300, y: 500, width: 100, height: 20, type: 'platform' },
                    { x: 500, y: 450, width: 100, height: 20, type: 'platform' },
                    { x: 700, y: 400, width: 100, height: 20, type: 'platform' },
                    { x: 900, y: 350, width: 150, height: 20, type: 'platform' },
                    { x: 1100, y: 550, width: 100, height: 50, type: 'ground' }
                ],
                collectibles: [
                    { x: 330, y: 470, width: 16, height: 16, type: 'coin' },
                    { x: 530, y: 420, width: 16, height: 16, type: 'coin' },
                    { x: 730, y: 370, width: 16, height: 16, type: 'coin' },
                    { x: 950, y: 320, width: 16, height: 16, type: 'coin' }
                ],
                enemies: [
                    { x: 320, y: 476, type: 'patrol' },
                    { x: 720, y: 376, type: 'patrol' }
                ],
                spikes: [
                    { x: 250, y: 530, width: 40, height: 20 },
                    { x: 650, y: 530, width: 60, height: 20 }
                ],
                goal: { x: 1120, y: 500, width: 30, height: 50 },
                background: '#87CEEB'
            },
            2: {
                name: "移动挑战",
                description: "掌握移动平台的节奏",
                playerStart: { x: 50, y: 400 },
                platforms: [
                    { x: 0, y: 550, width: 150, height: 50, type: 'ground' },
                    { x: 250, y: 500, width: 80, height: 20, type: 'moving', moveX: true, range: 100, speed: 2 },
                    { x: 450, y: 450, width: 80, height: 20, type: 'platform' },
                    { x: 600, y: 400, width: 80, height: 20, type: 'moving', moveY: true, range: 80, speed: 1.5 },
                    { x: 800, y: 350, width: 80, height: 20, type: 'disappearing', timer: 180 },
                    { x: 950, y: 300, width: 80, height: 20, type: 'moving', moveX: true, range: 120, speed: 2.5 },
                    { x: 1100, y: 550, width: 100, height: 50, type: 'ground' }
                ],
                collectibles: [
                    { x: 270, y: 470, width: 16, height: 16, type: 'coin' },
                    { x: 480, y: 420, width: 16, height: 16, type: 'coin' },
                    { x: 630, y: 370, width: 16, height: 16, type: 'coin' },
                    { x: 830, y: 320, width: 16, height: 16, type: 'coin' },
                    { x: 980, y: 270, width: 16, height: 16, type: 'coin' }
                ],
                enemies: [
                    { x: 470, y: 426, type: 'jump' },
                    { x: 200, y: 300, type: 'fly' }
                ],
                spikes: [
                    { x: 200, y: 530, width: 40, height: 20 },
                    { x: 400, y: 530, width: 40, height: 20 },
                    { x: 750, y: 530, width: 80, height: 20 }
                ],
                goal: { x: 1120, y: 500, width: 30, height: 50 },
                background: '#FFB6C1'
            },
            3: {
                name: "终极试炼",
                description: "所有技巧的综合考验",
                playerStart: { x: 50, y: 450 },
                platforms: [
                    { x: 0, y: 550, width: 120, height: 50, type: 'ground' },
                    { x: 180, y: 500, width: 60, height: 20, type: 'disappearing', timer: 120 },
                    { x: 300, y: 450, width: 80, height: 20, type: 'moving', moveX: true, range: 80, speed: 3 },
                    { x: 450, y: 400, width: 60, height: 20, type: 'platform' },
                    { x: 580, y: 350, width: 80, height: 20, type: 'moving', moveY: true, range: 100, speed: 2 },
                    { x: 720, y: 300, width: 60, height: 20, type: 'disappearing', timer: 90 },
                    { x: 850, y: 250, width: 80, height: 20, type: 'moving', moveX: true, range: 100, speed: 2.5 },
                    { x: 1000, y: 200, width: 60, height: 20, type: 'platform' },
                    { x: 1100, y: 550, width: 100, height: 50, type: 'ground' }
                ],
                collectibles: [
                    { x: 200, y: 470, width: 16, height: 16, type: 'coin' },
                    { x: 330, y: 420, width: 16, height: 16, type: 'coin' },
                    { x: 480, y: 370, width: 16, height: 16, type: 'coin' },
                    { x: 610, y: 320, width: 16, height: 16, type: 'coin' },
                    { x: 750, y: 270, width: 16, height: 16, type: 'coin' },
                    { x: 880, y: 220, width: 16, height: 16, type: 'coin' },
                    { x: 1030, y: 170, width: 16, height: 16, type: 'coin' }
                ],
                enemies: [
                    { x: 320, y: 426, type: 'patrol' },
                    { x: 470, y: 376, type: 'jump' },
                    { x: 600, y: 200, type: 'fly' },
                    { x: 870, y: 226, type: 'patrol' }
                ],
                spikes: [
                    { x: 140, y: 530, width: 30, height: 20 },
                    { x: 260, y: 530, width: 30, height: 20 },
                    { x: 520, y: 530, width: 50, height: 20 },
                    { x: 680, y: 530, width: 30, height: 20 },
                    { x: 950, y: 530, width: 40, height: 20 }
                ],
                goal: { x: 1120, y: 500, width: 30, height: 50 },
                background: '#DDA0DD'
            }
        };
    }

    getCurrentLevel() {
        return this.levels[this.currentLevel];
    }

    nextLevel() {
        if (this.currentLevel < Object.keys(this.levels).length) {
            this.currentLevel++;
            return true;
        }
        return false;
    }

    resetLevel() {
        // 重置当前关卡，不改变关卡号
    }

    setLevel(levelNum) {
        if (this.levels[levelNum]) {
            this.currentLevel = levelNum;
            return true;
        }
        return false;
    }
}

// 平台类
class Platform {
    constructor(data) {
        this.x = data.x;
        this.y = data.y;
        this.width = data.width;
        this.height = data.height;
        this.type = data.type || 'platform';

        // 移动平台属性
        this.originalX = data.x;
        this.originalY = data.y;
        this.moveX = data.moveX || false;
        this.moveY = data.moveY || false;
        this.range = data.range || 100;
        this.speed = data.speed || 1;
        this.direction = 1;
        this.moveTime = 0;

        // 消失平台属性
        this.timer = data.timer || 180;
        this.maxTimer = data.timer || 180;
        this.visible = true;
        this.touched = false;
    }

    update() {
        switch (this.type) {
            case 'moving':
                this.updateMoving();
                break;
            case 'disappearing':
                this.updateDisappearing();
                break;
        }
    }

    updateMoving() {
        this.moveTime += this.speed * 0.02;

        if (this.moveX) {
            this.x = this.originalX + Math.sin(this.moveTime) * this.range;
        }

        if (this.moveY) {
            this.y = this.originalY + Math.sin(this.moveTime) * this.range;
        }
    }

    updateDisappearing() {
        if (this.touched) {
            this.timer--;
            if (this.timer <= 0) {
                this.visible = false;
            }
        }
    }

    onPlayerTouch() {
        if (this.type === 'disappearing') {
            this.touched = true;
        }
    }

    reset() {
        this.x = this.originalX;
        this.y = this.originalY;
        this.timer = this.maxTimer;
        this.visible = true;
        this.touched = false;
        this.moveTime = 0;
        this.direction = 1;
    }

    draw(ctx) {
        if (!this.visible) return;

        ctx.save();

        // 消失平台的闪烁效果
        if (this.type === 'disappearing' && this.touched) {
            const alpha = this.timer / this.maxTimer;
            ctx.globalAlpha = alpha;

            if (this.timer < 60 && Math.floor(this.timer / 10) % 2) {
                ctx.globalAlpha = 0.3;
            }
        }

        // 根据类型绘制不同样式的平台
        switch (this.type) {
            case 'ground':
                this.drawGround(ctx);
                break;
            case 'moving':
                this.drawMovingPlatform(ctx);
                break;
            case 'disappearing':
                this.drawDisappearingPlatform(ctx);
                break;
            default:
                this.drawNormalPlatform(ctx);
                break;
        }

        ctx.restore();
    }

    drawGround(ctx) {
        // 地面
        ctx.fillStyle = '#8B4513';
        ctx.fillRect(this.x, this.y, this.width, this.height);

        // 草地表面
        ctx.fillStyle = '#228B22';
        ctx.fillRect(this.x, this.y, this.width, 8);

        // 草的细节
        for (let i = 0; i < this.width; i += 8) {
            ctx.fillStyle = '#32CD32';
            ctx.fillRect(this.x + i, this.y - 2, 2, 4);
            ctx.fillRect(this.x + i + 4, this.y - 3, 2, 5);
        }
    }

    drawNormalPlatform(ctx) {
        // 普通平台
        ctx.fillStyle = '#696969';
        ctx.fillRect(this.x, this.y, this.width, this.height);

        // 边框
        ctx.strokeStyle = '#2F4F4F';
        ctx.lineWidth = 2;
        ctx.strokeRect(this.x, this.y, this.width, this.height);

        // 表面纹理
        ctx.fillStyle = '#778899';
        ctx.fillRect(this.x + 2, this.y + 2, this.width - 4, 4);
    }

    drawMovingPlatform(ctx) {
        // 移动平台
        ctx.fillStyle = '#4169E1';
        ctx.fillRect(this.x, this.y, this.width, this.height);

        // 发光效果
        ctx.fillStyle = '#87CEFA';
        ctx.fillRect(this.x + 2, this.y + 2, this.width - 4, this.height - 4);

        // 移动指示器
        const glowIntensity = Math.sin(Date.now() * 0.01) * 0.3 + 0.7;
        ctx.fillStyle = `rgba(255, 255, 255, ${glowIntensity})`;
        ctx.fillRect(this.x + 4, this.y + 4, this.width - 8, 4);
    }

    drawDisappearingPlatform(ctx) {
        // 消失平台
        ctx.fillStyle = '#FF6347';
        ctx.fillRect(this.x, this.y, this.width, this.height);

        // 警告纹理
        if (this.touched) {
            ctx.fillStyle = '#FFB6C1';
            for (let i = 0; i < this.width; i += 8) {
                for (let j = 0; j < this.height; j += 8) {
                    if ((i + j) % 16 === 0) {
                        ctx.fillRect(this.x + i, this.y + j, 4, 4);
                    }
                }
            }
        }
    }
}

// 收集品类
class Collectible {
    constructor(data) {
        this.x = data.x;
        this.y = data.y;
        this.width = data.width;
        this.height = data.height;
        this.type = data.type;
        this.animFrame = 0;
        this.animTimer = 0;
        this.floatOffset = Math.random() * Math.PI * 2;
    }

    update() {
        this.animTimer++;
        if (this.animTimer >= 8) {
            this.animTimer = 0;
            this.animFrame = (this.animFrame + 1) % 8;
        }
    }

    draw(ctx) {
        const floatY = this.y + Math.sin(Date.now() * 0.005 + this.floatOffset) * 3;

        if (this.type === 'coin') {
            this.drawCoin(ctx, this.x, floatY);
        }
    }

    drawCoin(ctx, x, y) {
        // 金币旋转效果
        const rotation = this.animFrame * 0.25;
        const scaleX = Math.cos(rotation);

        ctx.save();
        ctx.translate(x + this.width/2, y + this.height/2);
        ctx.scale(scaleX, 1);

        // 外圈
        ctx.fillStyle = '#FFD700';
        ctx.beginPath();
        ctx.arc(0, 0, 8, 0, Math.PI * 2);
        ctx.fill();

        // 内圈
        ctx.fillStyle = '#FFA500';
        ctx.beginPath();
        ctx.arc(0, 0, 6, 0, Math.PI * 2);
        ctx.fill();

        // 中心符号
        ctx.fillStyle = '#FFD700';
        ctx.fillRect(-2, -4, 4, 8);
        ctx.fillRect(-4, -2, 8, 4);

        ctx.restore();
    }
}

// 尖刺类
class Spike {
    constructor(data) {
        this.x = data.x;
        this.y = data.y;
        this.width = data.width;
        this.height = data.height;
    }

    draw(ctx) {
        ctx.fillStyle = '#8B0000';

        // 绘制尖刺
        const spikeCount = Math.floor(this.width / 8);
        for (let i = 0; i < spikeCount; i++) {
            const spikeX = this.x + i * 8;
            ctx.beginPath();
            ctx.moveTo(spikeX, this.y + this.height);
            ctx.lineTo(spikeX + 4, this.y);
            ctx.lineTo(spikeX + 8, this.y + this.height);
            ctx.closePath();
            ctx.fill();

            // 高光
            ctx.fillStyle = '#FF4500';
            ctx.beginPath();
            ctx.moveTo(spikeX + 1, this.y + this.height - 2);
            ctx.lineTo(spikeX + 4, this.y + 2);
            ctx.lineTo(spikeX + 2, this.y + this.height - 2);
            ctx.closePath();
            ctx.fill();

            ctx.fillStyle = '#8B0000';
        }
    }
}
