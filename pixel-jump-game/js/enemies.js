// 敌人基类
class Enemy {
    constructor(x, y, type) {
        this.x = x;
        this.y = y;
        this.type = type;
        this.width = 20;
        this.height = 20;
        this.health = 1;
        this.speed = 1;
        this.direction = 1;
        this.animFrame = 0;
        this.animTimer = 0;
        this.dead = false;
        this.deathTimer = 0;
    }

    update(platforms) {
        if (this.dead) {
            this.deathTimer++;
            return this.deathTimer > 30; // 30帧后移除
        }

        this.move();
        this.checkPlatformCollisions(platforms);
        this.updateAnimation();
        return false;
    }

    move() {
        this.x += this.speed * this.direction;
    }

    checkPlatformCollisions(platforms) {
        // 检查是否到达平台边缘或撞墙
        let onPlatform = false;
        let hitWall = false;

        platforms.forEach(platform => {
            // 检查是否在平台上
            if (this.x + this.width > platform.x && 
                this.x < platform.x + platform.width &&
                this.y + this.height >= platform.y && 
                this.y + this.height <= platform.y + 10) {
                onPlatform = true;
            }

            // 检查墙壁碰撞
            if (this.intersects(platform)) {
                if (this.direction > 0 && this.x < platform.x) {
                    hitWall = true;
                } else if (this.direction < 0 && this.x > platform.x) {
                    hitWall = true;
                }
            }
        });

        // 如果到达边缘或撞墙，转向
        if (!onPlatform || hitWall) {
            this.direction *= -1;
        }
    }

    takeDamage() {
        this.health--;
        if (this.health <= 0) {
            this.dead = true;
        }
    }

    intersects(rect) {
        return this.x < rect.x + rect.width &&
               this.x + this.width > rect.x &&
               this.y < rect.y + rect.height &&
               this.y + this.height > rect.y;
    }

    updateAnimation() {
        this.animTimer++;
        if (this.animTimer >= 15) {
            this.animTimer = 0;
            this.animFrame = (this.animFrame + 1) % 2;
        }
    }

    draw(ctx) {
        if (this.dead) {
            // 死亡动画
            ctx.save();
            ctx.globalAlpha = 1 - (this.deathTimer / 30);
            ctx.translate(this.x + this.width/2, this.y + this.height/2);
            ctx.rotate(this.deathTimer * 0.2);
            ctx.translate(-this.width/2, -this.height/2);
        }

        this.drawEnemy(ctx);

        if (this.dead) {
            ctx.restore();
        }
    }

    drawEnemy(ctx) {
        // 基础敌人绘制，子类重写
        ctx.fillStyle = '#ff4444';
        ctx.fillRect(this.x, this.y, this.width, this.height);
    }
}

// 巡逻敌人
class PatrolEnemy extends Enemy {
    constructor(x, y) {
        super(x, y, 'patrol');
        this.speed = 1.5;
        this.width = 24;
        this.height = 24;
    }

    drawEnemy(ctx) {
        // 身体
        ctx.fillStyle = '#ff4444';
        ctx.fillRect(this.x + 2, this.y + 8, 20, 16);
        
        // 头部
        ctx.fillStyle = '#ff6666';
        ctx.fillRect(this.x + 4, this.y, 16, 12);
        
        // 眼睛
        ctx.fillStyle = '#000';
        ctx.fillRect(this.x + 6, this.y + 3, 3, 3);
        ctx.fillRect(this.x + 15, this.y + 3, 3, 3);
        
        // 腿部动画
        if (this.animFrame === 0) {
            ctx.fillStyle = '#cc3333';
            ctx.fillRect(this.x + 4, this.y + 20, 6, 4);
            ctx.fillRect(this.x + 14, this.y + 20, 6, 4);
        } else {
            ctx.fillStyle = '#cc3333';
            ctx.fillRect(this.x + 6, this.y + 20, 6, 4);
            ctx.fillRect(this.x + 12, this.y + 20, 6, 4);
        }
        
        // 方向指示
        if (this.direction > 0) {
            ctx.fillStyle = '#ffff00';
            ctx.fillRect(this.x + 20, this.y + 6, 2, 2);
        } else {
            ctx.fillStyle = '#ffff00';
            ctx.fillRect(this.x + 2, this.y + 6, 2, 2);
        }
    }
}

// 跳跃敌人
class JumpEnemy extends Enemy {
    constructor(x, y) {
        super(x, y, 'jump');
        this.speed = 0;
        this.vy = 0;
        this.gravity = 0.3;
        this.jumpPower = 8;
        this.jumpTimer = 0;
        this.jumpInterval = 120; // 2秒跳一次
        this.onGround = false;
        this.width = 20;
        this.height = 20;
    }

    move() {
        // 跳跃逻辑
        this.jumpTimer++;
        if (this.jumpTimer >= this.jumpInterval && this.onGround) {
            this.vy = -this.jumpPower;
            this.onGround = false;
            this.jumpTimer = 0;
        }

        // 重力
        this.vy += this.gravity;
        this.y += this.vy;
    }

    checkPlatformCollisions(platforms) {
        this.onGround = false;
        
        platforms.forEach(platform => {
            if (this.intersects(platform)) {
                if (this.vy > 0 && this.y < platform.y) {
                    this.y = platform.y - this.height;
                    this.vy = 0;
                    this.onGround = true;
                }
            }
        });
    }

    drawEnemy(ctx) {
        // 身体（圆形）
        ctx.fillStyle = '#44ff44';
        ctx.beginPath();
        ctx.arc(this.x + this.width/2, this.y + this.height/2, this.width/2 - 2, 0, Math.PI * 2);
        ctx.fill();
        
        // 眼睛
        ctx.fillStyle = '#000';
        ctx.fillRect(this.x + 6, this.y + 6, 3, 3);
        ctx.fillRect(this.x + 11, this.y + 6, 3, 3);
        
        // 嘴巴
        ctx.fillStyle = '#000';
        ctx.fillRect(this.x + 7, this.y + 12, 6, 2);
        
        // 跳跃状态指示
        if (!this.onGround) {
            ctx.fillStyle = '#88ff88';
            ctx.fillRect(this.x + 2, this.y + this.height - 2, this.width - 4, 2);
        }
    }
}

// 飞行敌人
class FlyEnemy extends Enemy {
    constructor(x, y) {
        super(x, y, 'fly');
        this.speed = 2;
        this.amplitude = 30; // 上下移动幅度
        this.frequency = 0.05; // 上下移动频率
        this.baseY = y;
        this.time = 0;
        this.width = 18;
        this.height = 18;
    }

    move() {
        this.x += this.speed * this.direction;
        this.time += this.frequency;
        this.y = this.baseY + Math.sin(this.time) * this.amplitude;
    }

    checkPlatformCollisions(platforms) {
        // 飞行敌人只检查左右边界
        platforms.forEach(platform => {
            if (this.x + this.width > platform.x + platform.width + 50 ||
                this.x < platform.x - 50) {
                this.direction *= -1;
            }
        });
    }

    drawEnemy(ctx) {
        // 身体
        ctx.fillStyle = '#ff44ff';
        ctx.fillRect(this.x + 3, this.y + 6, 12, 8);
        
        // 翅膀动画
        const wingFlap = Math.sin(this.time * 10) > 0;
        ctx.fillStyle = '#ffaaff';
        
        if (wingFlap) {
            // 翅膀向上
            ctx.fillRect(this.x, this.y + 2, 4, 6);
            ctx.fillRect(this.x + 14, this.y + 2, 4, 6);
        } else {
            // 翅膀向下
            ctx.fillRect(this.x, this.y + 8, 4, 6);
            ctx.fillRect(this.x + 14, this.y + 8, 4, 6);
        }
        
        // 眼睛
        ctx.fillStyle = '#000';
        ctx.fillRect(this.x + 5, this.y + 7, 2, 2);
        ctx.fillRect(this.x + 11, this.y + 7, 2, 2);
        
        // 触角
        ctx.fillStyle = '#aa00aa';
        ctx.fillRect(this.x + 6, this.y + 2, 1, 3);
        ctx.fillRect(this.x + 11, this.y + 2, 1, 3);
        ctx.fillRect(this.x + 5, this.y, 1, 2);
        ctx.fillRect(this.x + 12, this.y, 1, 2);
    }
}

// 敌人工厂
class EnemyFactory {
    static createEnemy(type, x, y) {
        switch (type) {
            case 'patrol':
                return new PatrolEnemy(x, y);
            case 'jump':
                return new JumpEnemy(x, y);
            case 'fly':
                return new FlyEnemy(x, y);
            default:
                return new PatrolEnemy(x, y);
        }
    }
}
